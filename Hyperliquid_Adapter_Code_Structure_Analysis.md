# Hyperliquid Adapter Code Structure Analysis

## Overview

This document provides a comprehensive analysis of the Hyperliquid adapter code structure, mapping each file to its corresponding API documentation and ensuring complete coverage of the Hyperliquid API functionality.

## Current Directory Structure

```
adapters/hyperliquid/
├── __init__.py
├── common/                    # Core utilities and constants
│   ├── __init__.py
│   ├── constants.py          # API URLs and constants
│   ├── core.py               # Core functionality
│   ├── enums.py              # Hyperliquid-specific enumerations
│   └── types.py              # Type definitions
├── config.py                 # Configuration classes (EMPTY - needs implementation)
├── data.py                   # Data client implementation (EMPTY - needs implementation)
├── execution.py              # Execution client implementation (EMPTY - needs implementation)
├── http/                     # HTTP API layer
│   ├── __init__.py
│   ├── account.py            # Account-related HTTP operations
│   ├── client.py             # Main HTTP client with authentication
│   ├── error.py              # HTTP error handling
│   ├── exchange.py           # Exchange endpoint operations
│   └── market.py             # Market data HTTP operations
├── providers.py              # Instrument provider (EMPTY - needs implementation)
├── util/                     # Utility functions
│   ├── __init__.py
│   └── signing.py            # Signing utilities and authentication
└── websocket/                # WebSocket client
    ├── __init__.py
    └── client.py             # WebSocket implementation
```

## API Documentation Mapping

### 1. Info Endpoint (`docs/integrations/hyperliquid/info_endpoint.md`)

**Lines 1-313**: Comprehensive Info endpoint documentation
- **Maps to**: `http/market.py` (needs implementation)
- **Covers**: Market data, user data, portfolio information

**Key Functions to Implement**:
- `get_all_mids()` - Lines 28-42: Get all mid prices
- `get_open_orders()` - Lines 44-65: Get user's open orders
- `get_user_fills()` - Lines 67-97: Get user's fill history
- `get_user_fills_by_time()` - Lines 99-110: Get fills by time range
- `get_order_status()` - Lines 112-148: Query order status
- `get_l2_book()` - Lines 150-180: Get L2 orderbook snapshot
- `get_candle_snapshot()` - Lines 182-211: Get candlestick data
- `get_sub_accounts()` - Lines 213-250: Get user's sub-accounts
- `get_portfolio()` - Lines 252-287: Query user's portfolio

### 2. Exchange Endpoint (`docs/integrations/hyperliquid/exchange_endpoint.md`)

**Lines 1-277**: Exchange endpoint for trading operations
- **Maps to**: `http/exchange.py` (needs implementation)
- **Covers**: Order management, account operations, transfers

**Key Functions to Implement**:
- `place_order()` - Lines 29-111: Submit orders
- `cancel_order()` - Lines 113-145: Cancel orders
- `cancel_by_cloid()` - Lines 147-166: Cancel by client order ID
- `modify_order()` - Lines 168-195: Modify existing orders
- `update_leverage()` - Lines 197-213: Update position leverage
- `usd_send()` - Lines 215-231: Core USDC transfers
- `withdraw()` - Lines 233-249: Initiate withdrawals

### 3. WebSocket API (`docs/integrations/hyperliquid/websocket.md`)

**Lines 1-246**: WebSocket real-time data streams
- **Maps to**: `websocket/client.py` (needs implementation)
- **Covers**: Real-time market data and user updates

**Key Subscription Types** (Lines 36-107):
- `allMids` - All mid prices
- `notification` - User notifications
- `webData2` - User aggregated info
- `candle` - Candlestick data
- `l2Book` - Order book data
- `trades` - Trade data
- `orderUpdates` - Order updates
- `userEvents` - User events
- `userFills` - User fills
- `userFundings` - User funding fees

### 4. Perpetuals Info Endpoint (`docs/integrations/hyperliquid/info_endpoint_perpetuals.md`)

**Lines 1-323**: Perpetuals-specific info endpoints
- **Maps to**: `http/market.py` (perpetuals section)
- **Covers**: Perpetuals metadata, asset contexts, funding rates

**Key Functions**:
- `get_perp_dexs()` - Lines 12-35: Get perpetual exchanges
- `get_meta()` - Lines 37-80: Get perpetuals metadata
- `get_meta_and_asset_ctxs()` - Lines 82-126: Get asset contexts
- `get_clearinghouse_state()` - Lines 128-188: Get account summary
- `get_funding_history()` - Lines 224-250: Get funding rate history

### 5. Signing Mechanism (`docs/integrations/hyperliquid/signing.md`)

**Lines 1-141**: Signing mechanism documentation
- **Maps to**: `util/signing.py` (IMPLEMENTED)
- **Covers**: Authentication, signature generation, error handling

**Implementation Status**: ✅ COMPLETE
- Lines 16-22: Signing methods implemented
- Lines 78-118: Example usage patterns
- Lines 120-140: Best practices covered

## Required Implementation Files

### 1. `config.py` - Configuration Classes
**Status**: EMPTY - Needs Implementation
**Maps to**: General configuration patterns from other adapters

```python
class HyperliquidDataClientConfig(LiveDataClientConfig, frozen=True):
    # API configuration
    # WebSocket settings
    # Rate limiting parameters

class HyperliquidExecClientConfig(LiveExecClientConfig, frozen=True):
    # Trading configuration
    # Authentication settings
    # Risk management parameters
```

### 2. `data.py` - Data Client Implementation
**Status**: EMPTY - Needs Implementation
**Maps to**:
- `info_endpoint.md` (Lines 1-313)
- `websocket.md` (Lines 1-246)
- `info_endpoint_perpetuals.md` (Lines 1-323)

```python
class HyperliquidDataClient(LiveMarketDataClient):
    # WebSocket subscriptions
    # Historical data requests
    # Real-time data handling
```

### 3. `execution.py` - Execution Client Implementation
**Status**: EMPTY - Needs Implementation
**Maps to**: `exchange_endpoint.md` (Lines 1-277)

```python
class HyperliquidExecutionClient(LiveExecutionClient):
    # Order management
    # Account operations
    # Position management
```

### 4. `providers.py` - Instrument Provider
**Status**: EMPTY - Needs Implementation
**Maps to**: `info_endpoint_perpetuals.md` (Lines 37-80)

```python
class HyperliquidInstrumentProvider(InstrumentProvider):
    # Instrument metadata loading
    # Symbol management
    # Asset ID mapping
```

### 5. `factories.py` - Factory Classes
**Status**: MISSING - Needs Creation
**Maps to**: Standard factory pattern from other adapters

```python
class HyperliquidLiveDataClientFactory(LiveDataClientFactory):
    # Data client creation

class HyperliquidLiveExecClientFactory(LiveExecClientFactory):
    # Execution client creation
```

## Implementation Priority

### Phase 1: Core Infrastructure
1. **`config.py`** - Configuration classes
2. **`factories.py`** - Factory implementations
3. **`providers.py`** - Instrument provider

### Phase 2: HTTP Layer Enhancement
1. **`http/market.py`** - Complete Info endpoint implementation
2. **`http/exchange.py`** - Complete Exchange endpoint implementation
3. **`http/account.py`** - Account-specific operations

### Phase 3: Client Implementations
1. **`data.py`** - Data client with WebSocket integration
2. **`execution.py`** - Execution client with order management
3. **`websocket/client.py`** - Enhanced WebSocket client

### Phase 4: Advanced Features
1. **Spot trading support** - Based on `info_endpoint_spot.md`
2. **Advanced WebSocket features** - Based on `websocket_advanced.md`
3. **Error handling enhancement** - Based on `error_responses.md`

## API Coverage Status

### ✅ Implemented
- **Signing mechanism** (`util/signing.py`)
- **Basic HTTP client** (`http/client.py`)
- **Constants and URLs** (`common/constants.py`)

### 🔄 Partially Implemented
- **HTTP layer structure** (files exist but need content)
- **WebSocket client structure** (basic structure exists)

### ❌ Missing Implementation
- **Configuration classes**
- **Data client**
- **Execution client**
- **Instrument provider**
- **Factory classes**
- **Complete HTTP endpoint implementations**
- **Complete WebSocket functionality**

## Documentation References

Each implementation should reference specific lines from the documentation:

1. **Info Endpoint**: `docs/integrations/hyperliquid/info_endpoint.md:1-313`
2. **Exchange Endpoint**: `docs/integrations/hyperliquid/exchange_endpoint.md:1-277`
3. **WebSocket API**: `docs/integrations/hyperliquid/websocket.md:1-246`
4. **Perpetuals Info**: `docs/integrations/hyperliquid/info_endpoint_perpetuals.md:1-323`
5. **Signing**: `docs/integrations/hyperliquid/signing.md:1-141`

This structure ensures complete API coverage and clear mapping between implementation files and documentation sections.

## Additional Documentation Mappings

### 6. Spot Trading (`docs/integrations/hyperliquid/info_endpoint_spot.md`)
**Status**: Not yet examined - Needs implementation
**Maps to**: `http/market.py` (spot section), `data.py` (spot subscriptions)
**Covers**: Spot market data, spot trading operations

### 7. Asset IDs (`docs/integrations/hyperliquid/asset_ids.md`)
**Maps to**: `common/types.py`, `providers.py`
**Covers**: Asset ID mapping for perpetuals and spot

### 8. Error Responses (`docs/integrations/hyperliquid/error_responses.md`)
**Maps to**: `http/error.py`, all client implementations
**Covers**: Error handling patterns and response codes

### 9. Nonces and API Wallets (`docs/integrations/hyperliquid/nonces_and_api_wallets.md`)
**Maps to**: `util/signing.py` (NonceManager), `http/client.py`
**Covers**: Nonce management and API wallet authentication

### 10. Tick and Lot Size (`docs/integrations/hyperliquid/tick_and_lot_size.md`)
**Maps to**: `providers.py`, `common/types.py`
**Covers**: Price and size precision handling

### 11. WebSocket Advanced (`docs/integrations/hyperliquid/websocket_advanced.md`)
**Maps to**: `websocket/client.py` (advanced features)
**Covers**: Advanced WebSocket features and optimizations

### 12. Symbol Notation (`docs/integrations/hyperliquid/HyperLiquid_API_符号表示法.md`)
**Maps to**: `common/types.py`, `providers.py`
**Covers**: Symbol format and notation standards

## Detailed Implementation Specifications

### `http/market.py` Implementation Map

```python
class HyperliquidMarketHttpAPI:
    # Info Endpoint Functions (info_endpoint.md:28-287)
    async def get_all_mids(self, dex: Optional[str] = None) -> Dict[str, str]:
        """Lines 28-42: Get all mid prices"""

    async def get_open_orders(self, user: str, dex: Optional[str] = None) -> List[Dict]:
        """Lines 44-65: Get user's open orders"""

    async def get_user_fills(self, user: str, aggregate_by_time: bool = False) -> List[Dict]:
        """Lines 67-97: Get user's fill history"""

    async def get_user_fills_by_time(self, user: str, start_time: int,
                                   end_time: Optional[int] = None) -> List[Dict]:
        """Lines 99-110: Get fills by time range"""

    async def get_order_status(self, user: str, oid: Union[int, str]) -> Dict:
        """Lines 112-148: Query order status"""

    async def get_l2_book(self, coin: str, n_sig_figs: Optional[int] = None,
                         mantissa: Optional[int] = None) -> List[List[Dict]]:
        """Lines 150-180: Get L2 orderbook snapshot"""

    async def get_candle_snapshot(self, coin: str, interval: str,
                                start_time: int, end_time: int) -> List[Dict]:
        """Lines 182-211: Get candlestick data"""

    # Perpetuals Functions (info_endpoint_perpetuals.md:12-323)
    async def get_perp_dexs(self) -> List[Optional[Dict]]:
        """Lines 12-35: Get perpetual exchanges"""

    async def get_meta(self, dex: Optional[str] = None) -> Dict:
        """Lines 37-80: Get perpetuals metadata"""

    async def get_meta_and_asset_ctxs(self) -> List[Union[Dict, List[Dict]]]:
        """Lines 82-126: Get metadata and asset contexts"""

    async def get_clearinghouse_state(self, user: str, dex: Optional[str] = None) -> Dict:
        """Lines 128-188: Get account summary"""

    async def get_funding_history(self, coin: str, start_time: int,
                                end_time: Optional[int] = None) -> List[Dict]:
        """Lines 224-250: Get funding rate history"""
```

### `http/exchange.py` Implementation Map

```python
class HyperliquidExchangeHttpAPI:
    # Exchange Endpoint Functions (exchange_endpoint.md:29-277)
    async def place_order(self, action: Dict, nonce: int, signature: Dict,
                         vault_address: Optional[str] = None,
                         expires_after: Optional[int] = None) -> Dict:
        """Lines 29-111: Submit orders"""

    async def cancel_order(self, cancels: List[Dict], nonce: int, signature: Dict,
                          vault_address: Optional[str] = None) -> Dict:
        """Lines 113-145: Cancel orders"""

    async def cancel_by_cloid(self, cancels: List[Dict], nonce: int, signature: Dict,
                             vault_address: Optional[str] = None) -> Dict:
        """Lines 147-166: Cancel by client order ID"""

    async def modify_order(self, oid: Union[int, str], order: Dict, nonce: int,
                          signature: Dict, vault_address: Optional[str] = None) -> Dict:
        """Lines 168-195: Modify existing orders"""

    async def update_leverage(self, asset: int, is_cross: bool, leverage: int,
                             nonce: int, signature: Dict,
                             vault_address: Optional[str] = None) -> Dict:
        """Lines 197-213: Update position leverage"""

    async def usd_send(self, destination: str, amount: str, nonce: int,
                      signature: Dict, hyperliquid_chain: str = "Mainnet") -> Dict:
        """Lines 215-231: Core USDC transfers"""

    async def withdraw(self, amount: str, destination: str, nonce: int,
                      signature: Dict, hyperliquid_chain: str = "Mainnet") -> Dict:
        """Lines 233-249: Initiate withdrawals"""
```

### `websocket/client.py` Implementation Map

```python
class HyperliquidWebSocketClient:
    # WebSocket Subscriptions (websocket.md:36-107)
    async def subscribe_all_mids(self, dex: Optional[str] = None):
        """Lines 40-44: Subscribe to all mid prices"""

    async def subscribe_notification(self, user: str):
        """Lines 46-48: Subscribe to user notifications"""

    async def subscribe_web_data2(self, user: str):
        """Lines 50-52: Subscribe to user aggregated info"""

    async def subscribe_candle(self, coin: str, interval: str):
        """Lines 54-56: Subscribe to candlestick data"""

    async def subscribe_l2_book(self, coin: str, n_sig_figs: Optional[int] = None,
                               mantissa: Optional[int] = None):
        """Lines 58-61: Subscribe to order book data"""

    async def subscribe_trades(self, coin: str):
        """Lines 63-65: Subscribe to trade data"""

    async def subscribe_order_updates(self, user: str):
        """Lines 67-69: Subscribe to order updates"""

    async def subscribe_user_events(self, user: str):
        """Lines 71-73: Subscribe to user events"""

    async def subscribe_user_fills(self, user: str, aggregate_by_time: bool = False):
        """Lines 75-78: Subscribe to user fills"""

    # Message Handlers (websocket.md:108-176)
    def _handle_trade_message(self, data: Dict):
        """Lines 115-127: Handle trade messages"""

    def _handle_book_message(self, data: Dict):
        """Lines 129-134: Handle order book messages"""

    def _handle_user_fills_message(self, data: Dict):
        """Lines 150-175: Handle user fills messages"""
```

## Implementation Guidelines by File

### 1. `common/enums.py` - Enumerations
**Reference**: Multiple docs for enum values
- Order types from `exchange_endpoint.md:29-111`
- Time in force from `signing.md:107-113`
- WebSocket channels from `websocket.md:36-107`

### 2. `common/types.py` - Type Definitions
**Reference**:
- `asset_ids.md` for asset ID types
- `tick_and_lot_size.md` for precision types
- `websocket.md:114-176` for WebSocket message types

### 3. `http/error.py` - Error Handling
**Reference**: `error_responses.md` (complete file)
- HTTP error codes and messages
- API-specific error handling patterns
- Retry logic for different error types

### 4. `util/signing.py` - Enhanced Signing
**Reference**: `nonces_and_api_wallets.md`, `signing.md:1-141`
- Current implementation is comprehensive
- May need enhancements for API wallet support
- Nonce management improvements

## Testing Strategy

### Unit Tests Required
1. **Signing functionality** - Test against known signatures
2. **HTTP client** - Mock API responses
3. **WebSocket client** - Mock WebSocket messages
4. **Data parsing** - Test all response formats
5. **Error handling** - Test all error scenarios

### Integration Tests Required
1. **Testnet connectivity** - Use testnet endpoints
2. **Authentication flow** - Test with real credentials
3. **WebSocket subscriptions** - Test real-time data
4. **Order lifecycle** - Test complete order flow
5. **Error recovery** - Test reconnection logic

## Compliance and Security

### Security Requirements
1. **Private key protection** - Never log or expose
2. **Nonce uniqueness** - Prevent replay attacks
3. **Signature validation** - Verify before sending
4. **Rate limiting** - Respect API limits
5. **Connection security** - Use secure WebSocket connections

### Compliance Requirements
1. **API documentation adherence** - Follow exact specifications
2. **Error handling standards** - Handle all documented errors
3. **Data format compliance** - Match expected formats exactly
4. **Version compatibility** - Support current API version

This comprehensive mapping ensures that every aspect of the Hyperliquid API is properly implemented and documented.
