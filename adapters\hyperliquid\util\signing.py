import hashlib
import json
import threading
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Tuple, TypedDict, Union

import msgspec
from eth_account import Account
from eth_account.messages import encode_typed_data
from eth_utils import keccak, to_hex
from nautilus_trader.common.component import Live<PERSON><PERSON>


def float_to_wire(x: Union[str, float]) -> str:
    """
    Converts a float to a string representation for the API, ensuring proper precision.
    If already a string, returns as is.
    
    Args:
        x: The float value to convert or a string to pass through
        
    Returns:
        The string representation of the float with proper precision
    """
    if isinstance(x, str):
        return x
        
    rounded = f"{x:.8f}"
    if abs(float(rounded) - x) >= 1e-12:
        raise ValueError(f"float_to_wire causes rounding: {x}")
    if rounded == "-0":
        rounded = "0"
    normalized = Decimal(rounded).normalize()
    return f"{normalized:f}"


L1_AGENT_EIP712_DOMAIN: Dict[str, Any] = {
    "name": "Exchange",
    "version": "1",
    "chainId": 1337,
    "verifyingContract": "******************************************",
}

L1_AGENT_MESSAGE_TYPE: Dict[str, Any] = {
    "Agent": [
        {"name": "source", "type": "string"},
        {"name": "connectionId", "type": "bytes32"},
    ]
}

EIP712_DOMAIN_TYPE_SCHEMA: List[Dict[str, str]] = [
    {"name": "name", "type": "string"},
    {"name": "version", "type": "string"},
    {"name": "chainId", "type": "uint256"},
    {"name": "verifyingContract", "type": "address"},
]

# User signed transaction types
USD_SEND_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "destination", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "time", "type": "uint64"},
]

SPOT_TRANSFER_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "destination", "type": "string"},
    {"name": "token", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "time", "type": "uint64"},
]

WITHDRAW_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "destination", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "time", "type": "uint64"},
]

USD_CLASS_TRANSFER_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "amount", "type": "string"},
    {"name": "toPerp", "type": "bool"},
    {"name": "nonce", "type": "uint64"},
]

APPROVE_BUILDER_FEE_SIGN_TYPES: List[Dict[str, str]] = [
    {"name": "hyperliquidChain", "type": "string"},
    {"name": "maxFeeRate", "type": "string"},
    {"name": "builder", "type": "address"},
    {"name": "nonce", "type": "uint64"},
]


class OrderType(str, Enum):
    """Order types supported by HyperLiquid."""
    LIMIT = "limit"
    MARKET = "market"
    STOP_MARKET = "stopMarket"
    STOP_LIMIT = "stopLimit"
    TAKE_PROFIT_MARKET = "takeProfitMarket"
    TAKE_PROFIT_LIMIT = "takeProfitLimit"


class TimeInForce(str, Enum):
    """Time in force options for limit orders."""
    GTC = "Gtc"  # Good till cancelled
    IOC = "Ioc"  # Immediate or cancel
    FOK = "Fok"  # Fill or kill
    POST_ONLY = "PostOnly"  # Post only


class HyperliquidSigner:
    """
    Handles signing of messages according to HyperLiquid's specific EIP-712 and hashing scheme.
    Ref:
        https://github.com/hyperliquid-dex/hyperliquid-python-sdk/blob/a8edca1ea20b6efe8235f4bbd9d6e9096e3aede6/hyperliquid/utils/signing.py
    """
    def __init__(
        self, 
        clock: LiveClock,
        private_key_hex: str, 
        is_mainnet: bool = True,
        vault_address: Optional[str] = None,
    ):
        """
        Initializes the signer with a private key and network settings.

        Args:
            clock: Optional LiveClock for timestamp generation. If None, system time will be used.
            private_key_hex: The private key as a hexadecimal string (e.g., "0x...").
            is_mainnet: Whether to use mainnet (True) or testnet (False) for signatures.
            vault_address: Optional default vault address for actions that require it.
        """
        self._clock = clock
        if not private_key_hex.startswith("0x"):
            private_key_hex = "0x" + private_key_hex
        try:
            self._account = Account.from_key(private_key_hex)
        except Exception as e:
            raise ValueError(f"Invalid private key: {e}")

        self._is_mainnet = is_mainnet
        self._vault_address = vault_address

    @staticmethod
    def address_to_bytes(address: str) -> bytes:
        """
        Converts an Ethereum address to bytes.

        Args:
            address: Ethereum address as a hex string.

        Returns:
            The address as bytes.
        """
        return bytes.fromhex(address[2:] if address.startswith("0x") else address)

    @staticmethod
    def action_hash(action: Dict[str, Any], vault_address: Optional[str], nonce: int, expires_after: Optional[int] = None) -> bytes:
        """
        Computes the hash of an action for signing.

        Args:
            action: The action to hash.
            vault_address: Optional vault address.
            nonce: The nonce value.
            expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            The hash as bytes.
        """
        data = msgspec.msgpack.encode(action)
        data += nonce.to_bytes(8, "big")
        if vault_address is None:
            data += b"\x00"  # Single null byte
        else:
            data += b"\x01"  # Single byte 0x01
            data += HyperliquidSigner.address_to_bytes(vault_address)

        # Add expiration time if provided
        if expires_after is not None:
            data += b"\x00"  # Marker for expiration time
            data += expires_after.to_bytes(8, "big")

        return keccak(data)

    @staticmethod
    def construct_phantom_agent(hash_bytes: bytes, is_mainnet: bool) -> Dict[str, Any]:
        """
        Constructs a phantom agent message for signing.

        Args:
            hash_bytes: The hash bytes.
            is_mainnet: Whether this is for mainnet.

        Returns:
            The phantom agent message.
        """
        return {"source": "a" if is_mainnet else "b", "connectionId": hash_bytes}

    def sign_inner(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        Signs EIP-712 structured data.

        Args:
            data: The structured data to sign.

        Returns:
            A dictionary with r, s, v components of the signature.
        """
        structured_data = encode_typed_data(full_message=data)
        signed = self._account.sign_message(structured_data)
        return {"r": hex(signed["r"]), "s": hex(signed["s"]), "v": signed["v"]}

    def sign_l1_action(
        self,
        action: Dict[str, Any],
        vault_address: Optional[str] = None,
        nonce: Optional[int] = None,
        is_mainnet: Optional[bool] = None,
        expires_after: Optional[int] = None
    ) -> Dict[str, str]:
        """
        Signs an L1 action for HyperLiquid.

        Args:
            action: The action to sign.
            vault_address: Optional vault address. Defaults to the signer's vault_address if set.
            nonce: The nonce value (if None, current timestamp will be used).
            is_mainnet: Whether this is for mainnet. Defaults to the signer's is_mainnet setting.
            expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            The structured data to sign.
        """
        # Use instance defaults if not explicitly provided
        if vault_address is None:
            vault_address = self._vault_address
        
        if is_mainnet is None:
            is_mainnet = self._is_mainnet

        # Generate timestamp if nonce is not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action_hash = self.action_hash(action, vault_address, nonce, expires_after)
        phantom_agent = self.construct_phantom_agent(action_hash, is_mainnet)

        data = {
            "domain": L1_AGENT_EIP712_DOMAIN,
            "types": {
                "EIP712Domain": EIP712_DOMAIN_TYPE_SCHEMA,
                "Agent": L1_AGENT_MESSAGE_TYPE["Agent"],
            },
            "primaryType": "Agent",
            "message": phantom_agent,
        }

        return self.sign_inner(data)

    def sign_order(
        self,
        asset_id: int,
        is_buy: bool,
        price: Union[str, float],
        size: Union[str, float],
        order_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs an order action with simplified parameters.

        Args:
            asset_id: The asset ID.
            is_buy: Whether this is a buy order.
            price: The price as a string or float.
            size: The size as a string or float.
            order_params: Optional dictionary with additional order parameters:
                - reduce_only: Whether this is a reduce-only order (default: False).
                - order_type: The order type (default: OrderType.LIMIT).
                - time_in_force: The time in force (default: TimeInForce.GTC).
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - client_id: Optional client ID.
                - trigger_price: Optional trigger price for stop/take-profit orders.
                - grouping: Order grouping (default: "na").
                - expires_after: Optional expiration time in milliseconds since epoch.
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signed order payload.
        """
        # Default parameters
        if order_params is None:
            order_params = {}
            
        reduce_only = order_params.get("reduce_only", False)
        order_type = order_params.get("order_type", OrderType.LIMIT)
        time_in_force = order_params.get("time_in_force", TimeInForce.GTC)
        vault_address = order_params.get("vault_address", self._vault_address)
        nonce = order_params.get("nonce", None)
        is_mainnet = order_params.get("is_mainnet", self._is_mainnet)
        client_id = order_params.get("client_id", None)
        trigger_price = order_params.get("trigger_price", None)
        grouping = order_params.get("grouping", "na")
        expires_after = order_params.get("expires_after", None)
        
        # Convert price and size to wire format
        price_str = float_to_wire(price)
        size_str = float_to_wire(size)

        # Convert trigger price to wire format if provided
        trigger_price_str = None
        if trigger_price is not None:
            trigger_price_str = float_to_wire(trigger_price)

        # Construct order wire format
        order_wire = {
            "a": asset_id,
            "b": is_buy,
            "p": price_str,
            "s": size_str,
            "r": reduce_only,
        }

        # Add order type specific fields
        if order_type == OrderType.LIMIT:
            order_wire["t"] = {"limit": {"tif": time_in_force}}
        elif order_type == OrderType.MARKET:
            order_wire["t"] = {"market": {}}
        elif order_type == OrderType.STOP_MARKET:
            if not trigger_price_str:
                raise ValueError("trigger_price is required for stop market orders")
            order_wire["t"] = {"stopMarket": {"trigger": trigger_price_str}}
        elif order_type == OrderType.STOP_LIMIT:
            if not trigger_price_str:
                raise ValueError("trigger_price is required for stop limit orders")
            order_wire["t"] = {"stopLimit": {"trigger": trigger_price_str, "tif": time_in_force}}
        elif order_type == OrderType.TAKE_PROFIT_MARKET:
            if not trigger_price_str:
                raise ValueError("trigger_price is required for take profit market orders")
            order_wire["t"] = {"takeProfitMarket": {"trigger": trigger_price_str}}
        elif order_type == OrderType.TAKE_PROFIT_LIMIT:
            if not trigger_price_str:
                raise ValueError("trigger_price is required for take profit limit orders")
            order_wire["t"] = {"takeProfitLimit": {"trigger": trigger_price_str, "tif": time_in_force}}

        # Add client ID if provided
        if client_id:
            order_wire["c"] = client_id

        # Construct the action
        action = {
            "type": "order",
            "orders": [order_wire],
            "grouping": grouping,
        }

        # Sign the action
        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_cancel_order(
        self,
        client_id: str,
        cancel_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a cancel order action with simplified parameters.

        Args:
            client_id: The client ID of the order to cancel.
            cancel_params: Optional dictionary with additional cancel parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signed cancel order payload.
        """
        # Default parameters
        if cancel_params is None:
            cancel_params = {}
            
        vault_address = cancel_params.get("vault_address", self._vault_address)
        nonce = cancel_params.get("nonce", None)
        is_mainnet = cancel_params.get("is_mainnet", self._is_mainnet)
        expires_after = cancel_params.get("expires_after", None)
        
        action = {
            "type": "cancelByCloid",
            "cancels": [client_id],
        }

        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_update_leverage(
        self,
        asset_id: int,
        leverage: Union[str, float],
        leverage_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs an update leverage action with simplified parameters.

        Args:
            asset_id: The asset ID.
            leverage: The leverage as a string or float.
            leverage_params: Optional dictionary with additional parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signed update leverage payload.
        """
        # Default parameters
        if leverage_params is None:
            leverage_params = {}
            
        vault_address = leverage_params.get("vault_address", self._vault_address)
        nonce = leverage_params.get("nonce", None)
        is_mainnet = leverage_params.get("is_mainnet", self._is_mainnet)
        expires_after = leverage_params.get("expires_after", None)
        
        # Convert leverage to wire format
        leverage_str = float_to_wire(leverage)

        action = {
            "type": "updateLeverage",
            "coin": asset_id,
            "leverage": leverage_str,
        }

        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_withdraw(
        self,
        amount: Union[str, float],
        destination: str,
        withdraw_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a withdraw action with simplified parameters.

        Args:
            amount: The amount to withdraw as a string or float.
            destination: The destination address.
            withdraw_params: Optional dictionary with additional parameters:
                - vault_address: Optional vault address.
                - nonce: The nonce value (if None, current timestamp will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).
                - expires_after: Optional expiration time in milliseconds since epoch.

        Returns:
            A dictionary with the signed withdraw payload.
        """
        # Default parameters
        if withdraw_params is None:
            withdraw_params = {}
            
        vault_address = withdraw_params.get("vault_address", self._vault_address)
        nonce = withdraw_params.get("nonce", None)
        is_mainnet = withdraw_params.get("is_mainnet", self._is_mainnet)
        expires_after = withdraw_params.get("expires_after", None)
        
        # Convert amount to wire format
        amount_str = float_to_wire(amount)

        action = {
            "type": "withdraw",
            "amount": amount_str,
            "destination": destination,
        }

        return self.sign_l1_action(action, vault_address, nonce, is_mainnet, expires_after)

    def sign_user_signed_action(
        self,
        action: Dict[str, Any],
        payload_types: List[Dict[str, str]],
        primary_type: str,
        is_mainnet: Optional[bool] = None,
    ) -> Dict[str, str]:
        """
        Signs a user-signed action for HyperLiquid.

        Args:
            action: The action to sign.
            payload_types: The payload types for the action.
            primary_type: The primary type for the action.
            is_mainnet: Whether this is for mainnet. Defaults to the signer's is_mainnet setting.

        Returns:
            The structured data to sign.
        """
        # Use instance defaults if not explicitly provided
        if is_mainnet is None:
            is_mainnet = self._is_mainnet
            
        # Add chain information
        action = action.copy()
        action["signatureChainId"] = "0x66eee"  # Can be any chain
        action["hyperliquidChain"] = "Mainnet" if is_mainnet else "Testnet"

        # Create the payload
        data = {
            "domain": {
                "name": "HyperliquidSignTransaction",
                "version": "1",
                "chainId": int(action["signatureChainId"], 16),
                "verifyingContract": "******************************************",
            },
            "types": {
                "EIP712Domain": EIP712_DOMAIN_TYPE_SCHEMA,
                primary_type: payload_types,
            },
            "primaryType": primary_type,
            "message": action,
        }

        return  self.sign_inner(data)

    def sign_usd_transfer_action(
        self,
        destination: str,
        amount: Union[str, float],
        transfer_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs a USD transfer action with simplified parameters.

        Args:
            destination: The destination address.
            amount: The amount to transfer as a string or float.
            transfer_params: Optional dictionary with additional parameters:
                - time_ms: The time in milliseconds (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if transfer_params is None:
            transfer_params = {}
            
        is_mainnet = transfer_params.get("is_mainnet", self._is_mainnet)
        time_ms = transfer_params.get("time_ms", None)
        
        # Generate timestamp if not provided
        if time_ms is None:
                time_ms = self._clock.timestamp_ms()
        # Convert amount to wire format if it's a float
        amount_str = float_to_wire(amount)

        action = {
            "destination": destination,
            "amount": amount_str,
            "time": time_ms,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=USD_SEND_SIGN_TYPES,
            primary_type="HyperliquidTransaction:UsdSend",
            is_mainnet=is_mainnet,
        )

    def sign_approve_builder_fee(
        self,
        max_fee_rate: str,
        builder: str,
        fee_params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Signs an approve builder fee action with simplified parameters.

        Args:
            max_fee_rate: The maximum fee rate.
            builder: The builder address.
            fee_params: Optional dictionary with additional parameters:
                - nonce: The nonce (if None, current time will be used).
                - is_mainnet: Whether this is for mainnet (defaults to signer's setting).

        Returns:
            A dictionary with the signature and payload ready to be sent to the API.
        """
        # Default parameters
        if fee_params is None:
            fee_params = {}
            
        is_mainnet = fee_params.get("is_mainnet", self._is_mainnet)
        nonce = fee_params.get("nonce", None)
        
        # Generate timestamp if not provided
        if nonce is None:
            nonce = self._clock.timestamp_ms()

        action = {
            "maxFeeRate": max_fee_rate,
            "builder": builder,
            "nonce": nonce,
        }

        return self.sign_user_signed_action(
            action=action,
            payload_types=APPROVE_BUILDER_FEE_SIGN_TYPES,
            primary_type="HyperliquidTransaction:ApproveBuilderFee",
            is_mainnet=is_mainnet,
        )


def format_signature_to_hex_string(r: Union[int, bytes], s: Union[int, bytes], v: int) -> str:
    """
    Formats the signature components (r, s, v) into a single hex string,
    matching the format used in HyperLiquid Rust SDK tests (r_hex + s_hex + v_hex).

    Args:
        r: The r component of the signature (as int or bytes).
        s: The s component of the signature (as int or bytes).
        v: The v component of thesignature (as int).

    Returns:
        A concatenated hex string of r, s, and v.
    """
    # Replace eth_utils.to_bytes, assuming null-byte padding (b'\\0')
    if isinstance(r, int):
        r_bytes = r.to_bytes(32, 'big')
    elif isinstance(r, bytes):
        r_bytes = r.rjust(32, b'\x00')
    else:
        raise TypeError(f"Unsupported type for r: {type(r)}")

    if isinstance(s, int):
        s_bytes = s.to_bytes(32, 'big')
    elif isinstance(s, bytes):
        s_bytes = s.rjust(32, b'\x00')
    else:
        raise TypeError(f"Unsupported type for s: {type(s)}")

    # Replace eth_utils.to_hex(bytes)[2:] with bytes.hex()
    r_hex = r_bytes.hex()  # bytes.hex() does not include "0x"
    s_hex = s_bytes.hex()  # bytes.hex() does not include "0x"
    v_hex = format(v, 'x')    # v is small, typically 27 or 28 (1b or 1c)

    return f"{r_hex}{s_hex}{v_hex}"


def format_signature_dict(r: bytes, s: bytes, v: int) -> Dict[str, str]:
    """
    Formats the signature components (r, s, v) into a dictionary format
    used by HyperLiquid API.

    Args:
        r: The r component of the signature as bytes.
        s: The s component of the signature as bytes.
        v: The v component of the signature as int.

    Returns:
        A dictionary with r, s, v components as hex strings.
    """
    return {
        "r": '0x' + r.hex(),
        "s": '0x' + s.hex(),
        "v": v,
    }


def float_to_int_for_hashing(x: float) -> int:
    """
    Converts a float to an integer for hashing with 8 decimal places.

    Args:
        x: The float to convert.

    Returns:
        The float as an integer.
    """
    return float_to_int(x, 8)


def float_to_usd_int(x: float) -> int:
    """
    Converts a float to an integer for USD with 6 decimal places.

    Args:
        x: The float to convert.

    Returns:
        The float as an integer.
    """
    return float_to_int(x, 6)


def float_to_int(x: float, power: int) -> int:
    """
    Converts a float to an integer with the specified number of decimal places.

    Args:
        x: The float to convert.
        power: The number of decimal places.

    Returns:
        The float as an integer.
    """
    with_decimals = x * 10**power
    if abs(round(with_decimals) - with_decimals) >= 1e-3:
        raise ValueError(f"float_to_int causes rounding: {x}")

    return round(with_decimals)


class NonceManager:
    """
    Manages nonces for API requests, ensuring they are unique and time-based.
    HyperLiquid nonces must be in the range (T - 2 days, T + 1 day) where T is
    the transaction block's Unix millisecond timestamp, and greater than the
    minimum of the 100 highest nonces stored for the address.
    This manager provides a simpler client-side incrementing nonce that should
    generally satisfy the server-side requirements if requests are not sent
    too far out of order or with very old timestamps.
    """
    def __init__(self, clock: Optional[LiveClock] = None):
        """
        Initialize a new nonce manager.

        Args:
            clock: Optional LiveClock for timestamp generation. If None, system time will be used.
        """
        self._clock = clock
        self._last_nonce: int = self._current_timestamp_ms()
        self._lock = threading.Lock()
        self._nonce_window: List[int] = []  # Store recent nonces for potential retry logic
        self._max_window_size: int = 100  # Keep track of last 100 nonces

    def _current_timestamp_ms(self) -> int:
        """Returns the current time in milliseconds since the UNIX epoch."""
        return self._clock.timestamp_ms()

    def get_next_nonce(self) -> int:
        """
        Generates the next nonce.
        Ensures the nonce is always increasing and at least the current millisecond timestamp.
        Also maintains a window of recent nonces for potential retry logic.

        Returns:
            The next nonce value.
        """
        with self._lock:
            current_time_ms = self._current_timestamp_ms()
            # Ensure nonce is at least current time and greater than the last one
            self._last_nonce = max(self._last_nonce + 1, current_time_ms)

            # Add to window and maintain window size
            self._nonce_window.append(self._last_nonce)
            if len(self._nonce_window) > self._max_window_size:
                self._nonce_window.pop(0)

            return self._last_nonce

    def get_recent_nonces(self) -> List[int]:
        """
        Returns the list of recent nonces.

        Returns:
            A list of recent nonces.
        """
        with self._lock:
            return self._nonce_window.copy()

    def is_valid_nonce(self, nonce: int) -> bool:
        """
        Checks if a nonce is valid according to HyperLiquid's rules.

        Args:
            nonce: The nonce to check.

        Returns:
            True if the nonce is valid, False otherwise.
        """
        current_time_ms = self._current_timestamp_ms()

        # Check time window: (T - 2 days, T + 1 day)
        two_days_ms = 2 * 24 * 60 * 60 * 1000
        one_day_ms = 24 * 60 * 60 * 1000

        if nonce < current_time_ms - two_days_ms or nonce > current_time_ms + one_day_ms:
            return False

        # Check if greater than minimum of 100 highest nonces
        with self._lock:
            if self._nonce_window and nonce <= min(self._nonce_window):
                return False

        return True

# Example usage (for testing purposes, similar to Rust tests):
if __name__ == "__main__":
    # Test case from hyperliquid-rust-sdk/src/signature/create_signature.rs tests
    # fn test_sign_l1_action()
    test_private_key = "0xe908f86dbb4d55ac876378565aafeabc187f6690f046459397b17d9b9a19688e"

    # Test with system time
    signer = HyperliquidSigner(private_key_hex=test_private_key)
    print(f"Signer Address: {signer.address}")

    # Test with LiveClock
    clock = LiveClock()
    signer_with_clock = HyperliquidSigner(private_key_hex=test_private_key, clock=clock)
    print(f"Signer Address (with clock): {signer_with_clock.address}")

    connection_id_hex = "0xde6c4037798a4434ca03cd05f00e3b803126221375cd1e7eaaaf041768be06eb"
    connection_id_bytes = bytes.fromhex(connection_id_hex[2:])

    # Test Mainnet Signature
    r_main, s_main, v_main = signer.sign_l1_action_legacy(connection_id=connection_id_bytes, is_mainnet=True)
    mainnet_sig_str = format_signature_to_hex_string(r_main, s_main, v_main)
    expected_mainnet_sig = "fa8a41f6a3fa728206df80801a83bcbfbab08649cd34d9c0bfba7c7b2f99340f53a00226604567b98a1492803190d65a201d6805e5831b7044f17fd530aec7841c"
    print(f"Calculated Mainnet Signature: {mainnet_sig_str}")
    print(f"Expected Mainnet Signature:   {expected_mainnet_sig}")
    assert mainnet_sig_str == expected_mainnet_sig, "Mainnet signature mismatch"
    print("Mainnet signature matches Rust test output.")

    # Test Testnet Signature
    r_test, s_test, v_test = signer.sign_l1_action_legacy(connection_id=connection_id_bytes, is_mainnet=False)
    testnet_sig_str = format_signature_to_hex_string(r_test, s_test, v_test)
    expected_testnet_sig = "1713c0fc661b792a50e8ffdd59b637b1ed172d9a3aa4d801d9d88646710fb74b33959f4d075a7ccbec9f2374a6da21ffa4448d58d0413a0d335775f680a881431c"
    print(f"Calculated Testnet Signature: {testnet_sig_str}")
    print(f"Expected Testnet Signature:   {expected_testnet_sig}")
    assert testnet_sig_str == expected_testnet_sig, "Testnet signature mismatch"
    print("Testnet signature matches Rust test output.")

    # Example for a generic typed data signing (if you had other structures)
    # This demonstrates the core sign_typed_data method
    # You would define MY_DOMAIN and MY_MESSAGE_TYPE for other actions
    # For example, if UsdSend from Rust tests was to be signed with the same Sha256Proxy logic:
    # USD_SEND_DOMAIN = L1_AGENT_EIP712_DOMAIN # Or a different domain if specified
    # USD_SEND_MESSAGE_TYPE = {
    #     "UsdSend": [
    #         {"name": "signatureChainId", "type": "uint256"}, # U256 in Rust
    #         {"name": "hyperliquidChain", "type": "string"},
    #         {"name": "destination", "type": "string"}, # Address as string
    #         {"name": "amount", "type": "string"},
    #         {"name": "time", "type": "uint64"},
    #     ]
    # }
    # usd_send_payload = {
    #     "signatureChainId": 421614, # Example from Rust test
    #     "hyperliquidChain": "Testnet",
    #     "destination": "******************************************",
    #     "amount": "1",
    #     "time": 1690393044548,
    # }
    # r_usd, s_usd, v_usd = signer.sign_typed_data(
    #     domain_data=USD_SEND_DOMAIN, # Assuming same domain for this example
    #     message_types=USD_SEND_MESSAGE_TYPE,
    #     message_data=usd_send_payload
    # )
    # usd_sig_str = format_signature_to_hex_string(r_usd, s_usd, v_usd)
    # expected_usd_sig = "214d507bbdaebba52fa60928f904a8b2df73673e3baba6133d66fe846c7ef70451e82453a6d8db124e7ed6e60fa00d4b7c46e4d96cb2bd61fd81b6e8953cc9d21b"
    # print(f"Calculated UsdSend Signature: {usd_sig_str}")
    # print(f"Expected UsdSend Signature:   {expected_usd_sig}")
    # assert usd_sig_str == expected_usd_sig, "UsdSend signature mismatch"
    # print("UsdSend signature matches Rust test output.")

    # Test order signing with system time
    order_data = signer.sign_order(
        asset_id=0,  # BTC
        is_buy=True,
        price="30000",
        size="0.001",
        reduce_only=False,
        order_type=OrderType.LIMIT,
        time_in_force=TimeInForce.GTC,
        client_id="test_order_1",
        is_mainnet=True,
    )
    print(f"\nSigned Order Data: {json.dumps(order_data, indent=2)}")

    # Test order signing with LiveClock
    order_data_with_clock = signer_with_clock.sign_order(
        asset_id=0,  # BTC
        is_buy=True,
        price="30000",
        size="0.001",
        reduce_only=False,
        order_type=OrderType.LIMIT,
        time_in_force=TimeInForce.GTC,
        client_id="test_order_2",
        is_mainnet=True,
    )
    print(f"\nSigned Order Data (with clock): {json.dumps(order_data_with_clock, indent=2)}")

    print("\nNonce Manager Example:")
    # Test with system time
    nonce_manager = NonceManager()
    print(f"Nonce 1: {nonce_manager.get_next_nonce()}")
    threading.Event().wait(0.01)
    print(f"Nonce 2: {nonce_manager.get_next_nonce()}")
    print(f"Nonce 3: {nonce_manager.get_next_nonce()}")

    # Test with LiveClock
    nonce_manager_with_clock = NonceManager(clock=clock)
    print(f"\nNonce Manager with LiveClock:")
    print(f"Nonce 1: {nonce_manager_with_clock.get_next_nonce()}")
    threading.Event().wait(0.01)
    print(f"Nonce 2: {nonce_manager_with_clock.get_next_nonce()}")
    print(f"Nonce 3: {nonce_manager_with_clock.get_next_nonce()}")

